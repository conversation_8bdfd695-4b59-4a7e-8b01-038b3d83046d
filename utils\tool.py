from scapy.contrib.automotive.doip import DoIP
import binascii
from scapy.layers.inet import *
import socket as sc
import time
RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"

payload_types = [0x0000,0x0001,0x0002,0x0003,0x0004,0x0005,0x0006,0x0007,0x0008,0x4001,0x4002,0x4003,0x4004,0x8001,0x8003]


def int_to_hexstr(num, length=2):
    return '{:0{length}x}'.format(num, length=length)


def receiver(socket, timeout=2):
    resp = None
    socket.settimeout(timeout)
    try:
        resp = DoIP(socket.recv(2048))
        while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            resp = DoIP(socket.recv(2048))
    except sc.timeout:
        pass
    finally:
        return resp

def process_response(resp, logger):
    if resp == raw(DoIP(resp)):
        resp = DoIP(resp)
    else:
        resp1 = DoIP(resp)
        resp2 = resp.split(raw(DoIP(resp)))[-1]
        resp = DoIP(resp2) if len(resp2) > 0 else resp1
    my_logger(resp, 0, logger)
    return resp

def deal_8002_data(resp, logger, sendData, recvTime, socket, first = True):
    while resp and resp[DoIP].payload_type == 0x8002:
        if recvTime:
            time.sleep(recvTime)
        previous_msg = binascii.b2a_hex(resp[DoIP].previous_msg).decode('utf-8')
        previous_sendData = binascii.b2a_hex(raw(sendData[UDS])).decode('utf-8')

        if previous_msg[:len(previous_sendData)] == previous_sendData:
            previous_msg = previous_msg[len(previous_sendData):]

        if len(previous_msg) > 0 and '02fd8001' == previous_msg[:8]:
            resp = DoIP(binascii.a2b_hex(previous_msg))
            my_logger(resp, 0, logger)
            x = binascii.b2a_hex(raw(resp)).decode('utf-8')
            kk = previous_msg.split(x)
            if len(kk) > 1 and len(kk[-1]) > 0:
                resp = DoIP(binascii.a2b_hex(kk[-1]))
                my_logger(resp, 0, logger)
        else:
            if first:
                resp = process_response(socket.recv(2048), logger)
            else:
                break
    return resp

def deal_nrc78_data(socket, logger, pstarTimeout, doip, sendData, recvTime):
    resp = None
    startTime = time.time()
    lastSendTime = startTime
    sendInterval = 1.5

    while True:
        try:
            resp = socket.recv(2048)
            if resp:
                resp = process_response(resp,logger)
                if resp and resp[DoIP].payload_type == 0x8002:
                    resp = deal_8002_data(resp, logger, sendData, recvTime, socket, first = False)
                if resp and resp[DoIP].payload_type == 0x8001 and resp.haslayer('UDS') and (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                    startTime = time.time()
                if resp and resp[DoIP].payload_type != 0x8002 and (resp[DoIP].payload_type == 0x8001 and resp.haslayer('UDS') and ( resp[UDS].service != 0x7f or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode != 0x78))):
                    break
        except sc.timeout:
            currentTime = time.time()
            if currentTime - startTime > pstarTimeout:
                break
            elif currentTime - startTime <= pstarTimeout:
                if currentTime - lastSendTime > sendInterval:
                    socket.sendall(raw(doip / UDS(binascii.a2b_hex('3e80'))))
                    my_logger(doip / UDS(binascii.a2b_hex('3e80')), 1, logger)
                    sendData = doip / UDS(binascii.a2b_hex('3e80'))
                    lastSendTime = currentTime
    return resp

def my_receiver(socket, timeout, pstarTimeout, logger, my_service, recvTime, doip, sendData, app_instance=None):
    resp = None
    privious_resp = None

    socket.settimeout(timeout)
    try:
        raw_data = socket.recv(2048)

        # Check if this is a DoIP alive check request before processing
        if (len(raw_data) >= 8 and
            raw_data[0] == 0x02 and raw_data[1] == 0xfd and
            raw_data[2] == 0x00 and raw_data[3] == 0x07 and
            raw_data[4] == 0x00 and raw_data[5] == 0x00 and
            raw_data[6] == 0x00 and raw_data[7] == 0x00):

            # This is a DoIP alive check request
            
            # Send alive check response
            alive_response = bytes([0x02, 0xfd, 0x00, 0x08, 0x00, 0x00, 0x00, 0x02,
                                    (app_instance.source >> 8) & 0xff, app_instance.source & 0xff])
            socket.send(alive_response)

            # Log the alive check exchange
            logger.info('Rx【Alive】<-- 02 FD 00 07 00 00 00 00')
            response_hex = ' '.join(f'{b:02X}' for b in alive_response)
            logger.info(f'Tx【Alive】--> {response_hex}')

            # Try to receive the actual response after handling alive check
            try:
                raw_data = socket.recv(2048)
            except sc.timeout:
                return None

        resp = process_response(raw_data, logger)
        while True:
            if resp and resp[DoIP].payload_type == 0x8002 and my_service != 0x3e:
                resp = deal_8002_data(resp, logger, sendData, recvTime, socket)
            elif resp and resp.haslayer('UDS') and (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                privious_resp = resp
                resp = deal_nrc78_data(socket, logger, pstarTimeout, doip, sendData, recvTime)
            else:
                if resp is None and privious_resp is not None:
                    resp = privious_resp
                break
    except sc.timeout:
        pass
    finally:
        if resp and resp[DoIP].payload_type == 0x8001 and not resp.haslayer('UDS'):
            print(RED + 'Has not get UDS data, use set --delay' + RESET)
            return 'delay_error'
        return resp

def my_logger(data, send, logger):
    if hasattr(data,'source_address') and data[DoIP].source_address is not None:
        tx = data[DoIP].source_address
        data = binascii.b2a_hex(raw(data)).decode('utf-8').upper()
        data = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
        if send:
            logger.info('Tx【'+int_to_hexstr(tx,4)+'】--> '+data)
        else:
            logger.info('Rx【'+int_to_hexstr(tx,4)+'】<-- '+data)
    else:
        data = binascii.b2a_hex(raw(data)).decode('utf-8').upper()
        data = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
        if send:
            logger.info('Sender   --> '+data)
        else:
            logger.info('Receiver <-- '+data)